from fastapi import FastAPI
import logging
from app.routes import upload,  search


# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = FastAPI(title="Conversational Video Intelligence")

app.include_router(upload.router, prefix="/upload")
app.include_router(search.router, prefix="/ask")


logger.info("FastAPI application started with all routes loaded")