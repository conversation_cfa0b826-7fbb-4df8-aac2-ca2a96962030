import streamlit as st
import requests
import json
import tempfile
import os
from pathlib import Path
import time
import logging
from dotenv import load_dotenv
import streamlit.components.v1 as components
from faster_whisper import WhisperModel
import io
load_dotenv(override=True)

# Configuration
API_BASE_URL = "http://localhost:8000"

# Set up logging
logger = logging.getLogger(__name__)

# Initialize Faster-Whisper model (cached)
@st.cache_resource
def load_whisper_model():
    """Load and cache the Faster-Whisper model for speech recognition"""
    try:
        # You can use 'base', 'small', 'medium', or 'large-v2' depending on your hardware
        return WhisperModel("base", device="cpu", compute_type="int8")
    except Exception as e:
        logger.error(f"Failed to load Faster-Whisper model: {e}")
        return None

def transcribe_audio(audio_data):
    """Transcribe audio using Faster-Whisper"""
    try:
        model = load_whisper_model()
        if model is None:
            return "Error: Could not load speech recognition model"

        # Save audio data to temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as tmp_file:
            tmp_file.write(audio_data.getbuffer())
            tmp_file_path = tmp_file.name

        try:
            segments, info = model.transcribe(tmp_file_path)
            text = " ".join([segment.text for segment in segments]).strip()
            return text if text else "No speech detected"
        finally:
            os.unlink(tmp_file_path)
    except Exception as e:
        logger.error(f"Transcription error: {e}")
        return f"Error during transcription: {str(e)}"


def create_custom_video_player(stream_url, player_id, segment_info=None, height=420):
    """Create a custom HTML5 video player with HLS.js support"""
    if not stream_url:
        return None
    
    # Format segment info for display
    segment_title = ""
    if segment_info:
        start = segment_info.get('start', 0)
        end = segment_info.get('end', 0)
        content = segment_info.get('text', '').strip()
        segment_title = f"📺 {int(start//60)}:{int(start%60):02d}-{int(end//60)}:{int(end%60):02d}"
        if content and len(content) > 50:
            content = content[:50] + "..."
        if content:
            segment_title += f" | {content}"
    
    video_html = f"""
    <div style="position: relative; margin-bottom: 12px; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); max-width: 750px; margin: 0 auto 12px auto;">
        <div style="position: relative; padding-top: 42%; background: #000;">
            <video 
                id="{player_id}" 
                controls 
                style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;" 
                playsinline
                preload="metadata"
            >
                <source src="{stream_url}" type="application/x-mpegURL">
                <source src="{stream_url}" type="video/mp4">
                Your browser does not support HTML5 video.
            </video>
        </div>
        
        <div style="padding: 8px 12px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div style="font-size: 12px; font-weight: 500;" id="{player_id}_title">{segment_title}</div>
                <div style="font-size: 11px; font-family: monospace;" id="{player_id}_time">0:00 / 0:00</div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <script>
    (function() {{
        const video = document.getElementById("{player_id}");
        if (!video) return;

        function formatTime(seconds) {{
            if (!seconds || isNaN(seconds)) return "0:00";
            const minutes = Math.floor(seconds / 60);
            const secs = Math.floor(seconds % 60);
            return `${{minutes}}:${{secs.toString().padStart(2, '0')}}`;
        }}

        function updateVideoTime() {{
            const timeDisplay = document.getElementById("{player_id}_time");
            if (timeDisplay) {{
                const duration = video.duration || 0;
                const current = video.currentTime || 0;
                timeDisplay.innerText = formatTime(current) + " / " + formatTime(duration);
            }}
        }}

        // Initialize HLS.js or fallback to native
        if (Hls.isSupported()) {{
            const hls = new Hls({{
                enableWorker: true,
                lowLatencyMode: false,
                backBufferLength: 90
            }});
            
            hls.loadSource("{stream_url}");
            hls.attachMedia(video);
            
            hls.on(Hls.Events.MANIFEST_PARSED, function() {{
                console.log("HLS manifest loaded for {player_id}");
            }});
            
            hls.on(Hls.Events.ERROR, function(event, data) {{
                console.warn("HLS error for {player_id}:", data);
                if (data.fatal) {{
                    // Try fallback to native video
                    video.src = "{stream_url}";
                }}
            }});
        }} else if (video.canPlayType('application/vnd.apple.mpegurl')) {{
            // Safari native HLS
            video.src = "{stream_url}";
        }} else {{
            // Fallback for other formats
            video.src = "{stream_url}";
        }}

        // Event listeners
        video.addEventListener('timeupdate', updateVideoTime);
        video.addEventListener('loadedmetadata', updateVideoTime);
        video.addEventListener('durationchange', updateVideoTime);
        
        // Error handling
        video.addEventListener('error', function(e) {{
            console.error("Video error for {player_id}:", e);
            const timeDisplay = document.getElementById("{player_id}_time");
            if (timeDisplay) {{
                timeDisplay.innerText = "Error loading video";
                timeDisplay.style.color = "#ff6b6b";
            }}
        }});
    }})();
    </script>
    """
    
    return video_html

def main():
    st.set_page_config(
        page_title="Video Intelligence (Light)",
        page_icon="🎥",
        layout="wide"
    )
    
    st.title("🎥 Conversational Intelligence Pipeline ")
    st.markdown("YouTube video analysis with intelligent chat interface")
    st.markdown("🚀 Powered by FastAPI + VideoDB + Open-Source AI (Llama 2, Faster-Whisper)")

    
    # Initialize session state
    if 'video_id' not in st.session_state:
        st.session_state.video_id = None
    if 'video_ready' not in st.session_state:
        st.session_state.video_ready = False
    if 'video_title' not in st.session_state:
        st.session_state.video_title = None
    if 'chat_history' not in st.session_state:
        st.session_state.chat_history = []
    
    # Sidebar navigation
    with st.sidebar:
        st.header("📋 Menu")
        
        # Mode selection
        mode = st.radio(
            "Choose:",
            ["📺 Load Video", "💬 Chat"],
            index=0
        )
        
        st.divider()
        
        # Current video status
        if st.session_state.video_id:
            st.subheader("📋 Current Video")
            st.write(f"**ID:** {st.session_state.video_id[:8]}...")
            if st.session_state.video_title:
                title = st.session_state.video_title
                if len(title) > 25:
                    title = title[:25] + "..."
                st.write(f"**Title:** {title}")
            
            status = "✅ Ready" if st.session_state.video_ready else "⚠️ Not Ready"
            st.write(f"**Status:** {status}")
            
            # Video ID management buttons
            col1, col2 = st.columns(2)
            with col1:
                if st.button("🔄 Reset"):
                    for key in list(st.session_state.keys()):
                        del st.session_state[key]
                    st.rerun()
            
            with col2:
                # Copy video ID button (shows full ID in a text area for easy copying)
                if st.button("📋 Copy ID"):
                    st.text_area("Full Video ID:", st.session_state.video_id, height=60, key="copy_video_id")
        else:
            st.info("No video loaded")
            
      
        
        
        # Quick tips section
        with st.expander("💡 Quick Tips"):
            st.markdown("""
            **How to Use:**
            1. **Load Video**: Upload YouTube URL or click existing video
            2. **Start Chat**: Switch to Chat mode  
            3. **Ask Questions**: Type or speak your questions
            
            **Input Methods:**
            - 🎤 **Voice Input**: Select voice mode and record your question
            - ⌨️ **Text Input**: Select text mode and type your question
            - 🔄 **Switch Modes**: Use the radio buttons to choose your preferred input method
            
            **Video Features:**
            - 📺 YouTube video processing
            - 🎥 One-click video loading
            - 🎬 Professional video players
            - ⏰ Timestamped segments
            - 🔗 Direct video links
            """)
            

    
    # Main content
    if mode == "📺 Load Video":
        show_upload_section()
    elif mode == "💬 Chat":
        show_chat_section()

def show_upload_section():
    st.header("📺 Load Video")
    
    # Tabs for different input methods
    tab1, tab2 = st.tabs(["📺 YouTube URL", "🆔 Manual Video ID"])
    
    with tab1:
        # YouTube URL input
        youtube_url = st.text_input(
            "Enter YouTube URL",
            placeholder="https://www.youtube.com/watch?v=...",
            help="Paste a YouTube video URL for automatic processing"
        )
        
        if youtube_url:
            if st.button("🚀 Process Video", type="primary", key="process_youtube"):
                with st.spinner("Processing video..."):
                    try:
                        response = requests.post(
                            f"{API_BASE_URL}/upload/youtube",
                            json={"url": youtube_url}
                        )
                        
                        if response.status_code == 200:
                            result = response.json()
                            st.session_state.video_id = result["video_id"]
                            st.session_state.video_ready = result.get("status") == "success"
                            
                            # Store video title if available
                            video_info = result.get("video_info", {})
                            st.session_state.video_title = video_info.get("title", "Unknown")
                            
                            # Clear chat history for new video
                            st.session_state.chat_history = []
                            
                            st.success(f"✅ Video processed and ready for chat!")
                            
                            if not st.session_state.video_ready:
                                st.warning("⚠️ Processing completed but video may not be ready for questions")
                            else:
                                st.info("💬 Switch to Chat mode to start asking questions!")
                                
                        else:
                            st.error(f"❌ Processing failed: {response.text}")
                            
                    except Exception as e:
                        st.error(f"❌ Error: {str(e)}")
    
    with tab2:
        # Manual Video ID input
        st.info("🆔 Use this if you already have a video ID from a previous upload")
        
        # Show available videos first
        col1, col2 = st.columns([2, 1])
        with col1:
            st.markdown("**📹 Available Videos:**")
        with col2:
            if st.button("🔄 Refresh", key="refresh_upload_videos"):
                if 'video_list' in st.session_state:
                    del st.session_state.video_list
                st.rerun()
        
        # Display video selection
        try:
            if 'video_list' not in st.session_state:
                with st.spinner("Loading videos..."):
                    response = requests.get(f"{API_BASE_URL}/ask/get_video_id")
                    if response.status_code == 200:
                        st.session_state.video_list = response.json()
                    else:
                        st.session_state.video_list = {"videos": [], "error": "Failed to fetch"}
            
            video_data = st.session_state.get('video_list', {"videos": []})
            
            if video_data.get('videos'):
                
                for i, video in enumerate(video_data['videos'][:6]):  # Show max 6 videos
                    title = video.get('title', 'No title')
                    video_id = video.get('video_id', 'unknown')
                    
                    col1, col2 = st.columns([4, 1])
                    with col1:
                        if st.button(f"🎥 {title}", key=f"load_video_{i}", help=f"Load: {title}"):
                            st.session_state.video_id = video_id
                            st.session_state.video_ready = True
                            st.session_state.video_title = title
                            st.session_state.chat_history = []
                            st.success(f"✅ Video loaded! Switch to Chat mode to start asking questions.")
                            time.sleep(1)  # Brief pause to show success
                            st.rerun()
                    with col2:
                        st.caption(f"ID: {video_id[:14]}...")
                
                if len(video_data['videos']) > 6:
                    st.info(f"📝 Showing 6 of {len(video_data['videos'])} videos (displaying most recent).")
                        
            elif video_data.get('error'):
                st.warning(f"⚠️ {video_data['error']}")
            else:
                st.info("📭 No videos found in collection")
                
        except Exception as e:
            pass

def show_chat_section():
    st.header("💬 Chat with Video")
    
    # Input method info for first-time users
    if not st.session_state.chat_history:
        st.info("💬 **Choose your input method below:** Use voice recording 🎤 for hands-free questions or text input ⌨️ for typing. Switch between modes anytime!")
    
    if not st.session_state.video_id:
        st.info("👆 Upload a video first")
        return
    elif not st.session_state.video_ready:
        st.warning("⚠️ Video not ready for chat")
        return
    
    # Display chat history
    for message in st.session_state.chat_history:
        if message["role"] == "user":
            with st.chat_message("user"):
                # Show input method indicator
                input_type = message.get("input_type", "text")
                if input_type == "voice":
                    st.caption("🎤 Voice message")
                else:
                    st.caption("⌨️ Text message")
                st.write(message["content"])
        else:
            with st.chat_message("assistant"):
                st.write(message["content"])

                # Display video segments if available
                if "video_segments" in message and message["video_segments"]:
                    st.subheader("🎬 Relevant Video Segments")
                    
                    for i, segment in enumerate(message["video_segments"][:3]):
                        with st.expander(f"Segment {i+1}: {segment['formatted_time']} ({segment['content_type']})"):
                            st.write(f"**Time:** {segment['start']:.1f}s - {segment['end']:.1f}s")
                            st.write(f"**Relevance Score:** {segment['score']:.3f}")
                            st.write(f"**Content:** {segment['text']}")
                            
                            # Show video playback options with custom player
                            generated_stream_url = segment.get('generated_stream_url')
                            player_url = segment.get('player_url')
                            stream_url = segment.get('stream_url')
                            
                            # Priority order for video URLs
                            primary_url = generated_stream_url or player_url or stream_url
                            
                            if primary_url:
                                st.markdown("**🎬 Video Segment:**")
                                
                                # Create custom video player
                                player_id = f"video_player_{i}_{int(time.time())}"
                                video_html = create_custom_video_player(
                                    stream_url=primary_url,
                                    player_id=player_id,
                                    segment_info=segment
                                )
                                
                                if video_html:
                                    components.html(video_html, height=340)
                                else:
                                    st.error("⚠️ Could not create video player")
                                

                                
                                # Fallback links
                                with st.expander("🔗 Direct Links", expanded=False):
                                    if generated_stream_url:
                                        st.markdown(f"🎯 [Generated Stream (Timeline)]({generated_stream_url})")
                                    if player_url:
                                        st.markdown(f"📺 [Player URL]({player_url})")
                                    if stream_url:
                                        st.markdown(f"🔗 [Stream URL]({stream_url})")
                                        
                            else:
                                st.info("💡 No video available - this might be a text-only result or still processing")
    
    # Input Method Selection
    st.markdown("---")
    st.markdown("**💬 Ask your question:**")
    
    # Input mode selector
    input_mode = st.radio(
        "Choose input method:",
        ["⌨️ Type your question", "🎤 Record your question"],
        horizontal=True,
        key="input_mode_selector"
    )
    
    user_input = None
    audio_input = None
    
    # Show selected input method
    if input_mode == "🎤 Record your question":
        with st.container():
            st.markdown("**🎤 Voice Input Mode**")
            st.caption("Speak clearly and press stop when finished")
            audio_input = st.audio_input("Record your question", key="voice_question")
        
        # Process voice input if provided
        if audio_input:
            with st.chat_message("user"):
                st.audio(audio_input, format="audio/wav")
                with st.spinner("🎤 Transcribing your voice..."):
                    transcribed_text = transcribe_audio(audio_input)
                    st.write(f"**You said:** {transcribed_text}")
            
            # Use transcribed text as user input
            user_input = transcribed_text
            
            # Add to chat history
            st.session_state.chat_history.append({
                "role": "user", 
                "content": transcribed_text,
                "input_type": "voice"
            })
    
    else:  # Text input mode
        with st.container():
            st.markdown("**⌨️ Text Input Mode**")
            st.caption("Type your question and press Enter")
            user_input = st.chat_input("Type your question here...")
        
        # Process text input if provided
        if user_input:
            # Add user message
            st.session_state.chat_history.append({
                "role": "user", 
                "content": user_input,
                "input_type": "text"
            })
            
            # Display user message
            with st.chat_message("user"):
                st.write(user_input)
    
    if user_input:
        # Get AI response
        with st.chat_message("assistant"):
            with st.spinner("Analyzing video content..."):
                try:
                    # Prepare conversation history for API (exclude current question)
                    history_for_api = st.session_state.chat_history[:-1]
                    
                    question_data = {
                        "video_id": st.session_state.video_id,
                        "question": user_input,
                        "top_k": 3,
                        "history": history_for_api,
                        "use_enhanced_search": True  # Enable enhanced search
                    }
                    
                    response = requests.post(
                        f"{API_BASE_URL}/ask/",
                        json=question_data
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        answer = result["answer"]
                        video_segments = result.get("video_segments", [])
                        consolidated_text = result.get("consolidated_text", "")
                        
                        # Display answer
                        st.write(answer)
                        

                        # Display video segments if available
                        if video_segments:
                            st.subheader("🎬 Relevant Video Segments")
                            
                            for i, segment in enumerate(video_segments[:3]):
                                with st.expander(f"Segment {i+1}: {segment['formatted_time']} ({segment['content_type']})"):
                                    st.write(f"**Time:** {segment['start']:.1f}s - {segment['end']:.1f}s")
                                    st.write(f"**Type:** {segment['content_type'].title()} Content")
                                    st.write(f"**Relevance Score:** {segment['score']:.3f}")
                                    st.write(f"**Content:** {segment['text']}")
                                    
                                    # Show video playback options with custom player
                                    generated_stream_url = segment.get('generated_stream_url')
                                    player_url = segment.get('player_url')
                                    stream_url = segment.get('stream_url')
                                    
                                    # Priority order for video URLs
                                    primary_url = generated_stream_url or player_url or stream_url
                                    
                                    if primary_url:
                                        st.markdown("**🎬 Video Segment:**")
                                        
                                        # Create custom video player
                                        player_id = f"video_player_detail_{i}_{int(time.time())}"
                                        video_html = create_custom_video_player(
                                            stream_url=primary_url,
                                            player_id=player_id,
                                            segment_info=segment
                                        )
                                        
                                        if video_html:
                                            components.html(video_html, height=340)
                                        else:
                                            st.error("⚠️ Could not create video player")
                                        

                                        
                                        # Fallback links
                                        with st.expander("🔗 Direct Links", expanded=False):
                                            if generated_stream_url:
                                                st.markdown(f"🎯 [Generated Stream (Timeline)]({generated_stream_url})")
                                            if player_url:
                                                st.markdown(f"📺 [Player URL]({player_url})")
                                            if stream_url:
                                                st.markdown(f"🔗 [Stream URL]({stream_url})")
                                                
                                    else:
                                        st.info("💡 No video available - this might be a text-only result or still processing")
                        
                        # Add to chat history with video segments and consolidated text
                        assistant_message = {
                            "role": "assistant", 
                            "content": answer,
                            "video_segments": video_segments,
                            "consolidated_text": consolidated_text
                        }
                        st.session_state.chat_history.append(assistant_message)
                        
                        # Show response details in expander
                        with st.expander("📊 Response Details"):
                            metadata = result.get('search_metadata', {})
                            
                            # Display metrics in columns
                            detail_col1, detail_col2, detail_col3 = st.columns(3)
                            with detail_col1:
                                st.metric("Confidence", f"{result['confidence']:.2f}")
                                st.metric("Text Sources", f"{metadata.get('text_chunks', 0)} chunks")
                            with detail_col2:
                                st.metric("Video Segments", metadata.get('video_segments', 0))
                                st.metric("Total Sources", metadata.get('total_sources', 0))
                            with detail_col3:
                                enhanced_enabled = "✅" if metadata.get('enhanced_search_enabled', False) else "❌"
                                st.metric("Enhanced Search", enhanced_enabled)
                                consolidated_available = "✅" if consolidated_text else "❌"
                                st.metric("Consolidated Text", consolidated_available)

                    else:
                        error_msg = "Sorry, I couldn't process that question."
                        st.error(error_msg)
                        st.session_state.chat_history.append({"role": "assistant", "content": error_msg})
                        
                except Exception as e:
                    error_msg = f"Error: {str(e)}"
                    st.error(error_msg)
                    st.session_state.chat_history.append({"role": "assistant", "content": error_msg})
    
    # Chat management options in sidebar
    with st.sidebar:
        if st.session_state.chat_history:
            st.subheader("💬 Chat Management")
            st.write(f"Messages: {len(st.session_state.chat_history)}")
            
            if st.button("🗑️ Clear Chat"):
                st.session_state.chat_history = []
                st.rerun()
                
            # Export chat option
            if st.button("📥 Export Chat"):
                chat_text = ""
                for msg in st.session_state.chat_history:
                    role = "You" if msg["role"] == "user" else "AI"
                    chat_text += f"{role}: {msg['content']}\n"
                    
                    # Include consolidated text if available
                    if "consolidated_text" in msg and msg["consolidated_text"]:
                        chat_text += "Video Content with Timestamps:\n"
                        chat_text += msg["consolidated_text"] + "\n\n"
                    
                    # Include video segments in export
                    if "video_segments" in msg and msg["video_segments"]:
                        chat_text += "Video Segments:\n"
                        for i, segment in enumerate(msg["video_segments"][:3]):
                            time_info = f" ({segment.get('start', 'N/A')}s-{segment.get('end', 'N/A')}s)"
                            content = segment.get('text', 'No content')
                            chat_text += f"  - Segment {i+1}{time_info}: {content}\n"
                        chat_text += "\n"
                    
                    # Include legacy video results for backward compatibility
                    if "video_results" in msg and msg["video_results"]:
                        chat_text += "Video Results:\n"
                        for i, result in enumerate(msg["video_results"][:3]):
                            if isinstance(result, dict):
                                time_info = ""
                                if 'shot' in result:
                                    shot = result['shot']
                                    if isinstance(shot, dict):
                                        time_info = f" ({shot.get('start', 'N/A')}s-{shot.get('end', 'N/A')}s)"
                                    else:
                                        time_info = f" ({shot.start:.1f}s-{shot.end:.1f}s)"
                                content = result.get('text') or result.get('content') or 'No content'
                                chat_text += f"  - Segment {i+1}{time_info}: {content}\n"
                        chat_text += "\n"
                    chat_text += "\n"
                
                st.download_button(
                    "💾 Download Chat",
                    chat_text,
                    file_name=f"chat_{st.session_state.video_id[:8]}.txt",
                    mime="text/plain"
                )

if __name__ == "__main__":
    main() 