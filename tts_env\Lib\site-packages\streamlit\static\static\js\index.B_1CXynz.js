import{s as l,b2 as C,J as b,b3 as s,r as u,b4 as n,j as a,b5 as v,b6 as B,b7 as k}from"./index.CbQtRkVt.js";function S(o,r){switch(o){case s.XSMALL:return{padding:`${r.spacing.twoXS} ${r.spacing.sm}`,fontSize:r.fontSizes.sm};case s.SMALL:return{padding:`${r.spacing.twoXS} ${r.spacing.md}`};case s.LARGE:return{padding:`${r.spacing.md} ${r.spacing.md}`};default:return{padding:`${r.spacing.xs} ${r.spacing.md}`}}}const e=l("a",{target:"e1xsxz6b0"})(({containerWidth:o,size:r,theme:i})=>({display:"inline-flex",alignItems:"center",justifyContent:"center",fontWeight:i.fontWeights.normal,padding:`${i.spacing.xs} ${i.spacing.md}`,borderRadius:i.radii.button,minHeight:i.sizes.minElementHeight,margin:0,lineHeight:i.lineHeights.base,color:i.colors.primary,textDecoration:"none",width:o?"100%":"auto",userSelect:"none","&:visited":{color:i.colors.primary},"&:focus":{outline:"none"},"&:focus-visible":{boxShadow:`0 0 0 0.2rem ${b(i.colors.primary,.5)}`},"&:hover":{textDecoration:"none"},"&:active":{textDecoration:"none"},...S(r,i)})),L=l(e,{target:"e1xsxz6b1"})(({theme:o})=>({backgroundColor:o.colors.primary,color:o.colors.white,border:`${o.sizes.borderWidth} solid ${o.colors.primary}`,"&:hover":{backgroundColor:C(o.colors.primary,.05),color:o.colors.white},"&:active":{backgroundColor:"transparent",color:o.colors.primary},"&:visited:not(:active)":{color:o.colors.white},"&[disabled], &[disabled]:hover, &[disabled]:active, &[disabled]:visited":{borderColor:o.colors.borderColor,backgroundColor:o.colors.transparent,color:o.colors.fadedText40,cursor:"not-allowed"}})),$=l(e,{target:"e1xsxz6b2"})(({theme:o})=>({backgroundColor:o.colors.lightenedBg05,color:o.colors.bodyText,border:`${o.sizes.borderWidth} solid ${o.colors.borderColor}`,"&:visited":{color:o.colors.bodyText},"&:hover":{borderColor:o.colors.primary,color:o.colors.primary},"&:active":{color:o.colors.white,borderColor:o.colors.primary,backgroundColor:o.colors.primary},"&:focus:not(:active)":{borderColor:o.colors.primary,color:o.colors.primary},"&[disabled], &[disabled]:hover, &[disabled]:active":{borderColor:o.colors.borderColor,backgroundColor:o.colors.transparent,color:o.colors.fadedText40,cursor:"not-allowed"}})),z=l(e,{target:"e1xsxz6b3"})(({theme:o})=>({padding:o.spacing.none,backgroundColor:o.colors.transparent,color:o.colors.bodyText,border:"none","&:visited":{color:o.colors.bodyText},"&:hover":{color:o.colors.primary},"&:active":{color:o.colors.primary},"&:focus":{outline:"none"},"&:focus-visible":{color:o.colors.primary,boxShadow:`0 0 0 0.2rem ${b(o.colors.primary,.5)}`},"&[disabled], &[disabled]:hover, &[disabled]:active":{backgroundColor:o.colors.transparent,color:o.colors.fadedText40,cursor:"not-allowed"}}));function T({kind:o,size:r,disabled:i,containerWidth:t,children:c,autoFocus:p,href:g,rel:y,target:f,onClick:x}){let d=L;return o===n.SECONDARY?d=$:o===n.TERTIARY&&(d=z),a(d,{kind:o,size:r||s.MEDIUM,containerWidth:t||!1,disabled:i||!1,autoFocus:p||!1,href:g,target:f,rel:y,onClick:x,tabIndex:i?-1:0,"data-testid":`stBaseLinkButton-${o}`,children:c})}const w=u.memo(T);function R(o){const{element:r}=o;let i=n.SECONDARY;r.type==="primary"?i=n.PRIMARY:r.type==="tertiary"&&(i=n.TERTIARY);const t=c=>{r.disabled&&c.preventDefault()};return a(k,{className:"stLinkButton","data-testid":"stLinkButton",children:a(v,{help:r.help,containerWidth:r.useContainerWidth,children:a(w,{kind:i,size:s.SMALL,disabled:r.disabled,onClick:t,containerWidth:r.useContainerWidth,href:r.url,target:"_blank",rel:"noreferrer","aria-disabled":r.disabled,children:a(B,{icon:r.icon,label:r.label})})})})}const W=u.memo(R);export{W as default};
