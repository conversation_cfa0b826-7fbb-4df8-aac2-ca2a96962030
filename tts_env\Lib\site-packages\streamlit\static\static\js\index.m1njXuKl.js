import{s as c,r as s,L as f,b8 as b,z as x,j as t,b5 as k,b9 as C,C as L,D as v,aH as h}from"./index.CbQtRkVt.js";const T=c("div",{target:"e1ghu24d0"})(({containerWidth:r})=>({display:"flex",flexDirection:"column",width:r?"100%":"fit-content"})),w=c("a",{target:"e1ghu24d1"})(({disabled:r,isCurrentPage:e,containerWidth:i,theme:o})=>({textDecoration:"none",width:i?"100%":"fit-content",display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"flex-start",gap:o.spacing.sm,borderRadius:o.radii.button,paddingLeft:o.spacing.sm,paddingRight:o.spacing.sm,marginTop:o.spacing.threeXS,marginBottom:o.spacing.threeXS,lineHeight:o.lineHeights.menuItem,backgroundColor:e?o.colors.darkenedBgMix15:"transparent","&:hover":{backgroundColor:e?o.colors.darkenedBgMix25:o.colors.darkenedBgMix15},"&:active,&:visited,&:hover":{textDecoration:"none"},"&:focus":{outline:"none"},"&:focus-visible":{backgroundColor:o.colors.darkenedBgMix15},"@media print":{paddingLeft:o.spacing.none},...r?{borderColor:o.colors.borderColor,backgroundColor:o.colors.transparent,color:o.colors.fadedText40,cursor:"not-allowed","&:hover":{color:o.colors.fadedText40,backgroundColor:o.colors.transparent}}:{}})),y=c("span",{target:"e1ghu24d2"})(({disabled:r,theme:e})=>({color:e.colors.bodyText,overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis",display:"table-cell",...r?{borderColor:e.colors.borderColor,backgroundColor:e.colors.transparent,color:e.colors.fadedText40,cursor:"not-allowed"}:{}}));function S(r,e){return r===null&&e?!0:r===null&&!e?!1:r===!0}function P(r){const{onPageChange:e,currentPageScriptHash:i}=s.useContext(f),o=s.useContext(b),{colors:d}=x(),{disabled:a,element:n}=r,l=S(n.useContainerWidth,o),g=i===n.pageScriptHash,p=u=>{n.external?a&&u.preventDefault():(u.preventDefault(),a||e(n.pageScriptHash))};return t("div",{className:"stPageLink","data-testid":"stPageLink",children:t(k,{help:n.help,placement:C.TOP_RIGHT,containerWidth:l,children:t(T,{containerWidth:l,children:L(w,{"data-testid":"stPageLink-NavLink",disabled:a,isCurrentPage:g,containerWidth:l,href:n.page,target:n.external?"_blank":"",rel:"noreferrer",onClick:p,children:[n.icon&&t(v,{size:"lg",color:a?d.fadedText40:d.bodyText,iconValue:n.icon}),t(y,{disabled:a,children:t(h,{source:n.label,allowHTML:!1,isLabel:!0,boldLabel:g,largerLabel:!0,disableLinks:!0})})]})})})})}const H=s.memo(P);export{H as default};
