2025-08-01 11:26:43,397 - app.core.videodb - WARNING - VideoDB SDK not available, using mock implementation
2025-08-01 11:26:43,399 - app.core.videodb - ERROR - Failed to initialize VideoDB connection: No API key provided. Set an API key either as an environment variable (VIDEO_DB_API_KEY) or pass it as an argument. 
2025-08-01 11:29:19,250 - app.core.videodb - WARNING - VideoDB SDK not available, using mock implementation
2025-08-01 11:29:19,253 - app.core.videodb - INFO - Connected to VideoDB successfully
2025-08-01 11:29:20,331 - app.core.videodb - INFO - Found 1 existing collections
2025-08-01 11:29:21,122 - app.core.videodb - INFO - Created new collection: convo ai (ID: c-2ac15f3e-4308-4895-9088-329dd7caf108)
2025-08-01 11:29:21,127 - app.core.videodb - INFO - Created new collection: convo ai (ID: c-2ac15f3e-4308-4895-9088-329dd7caf108)
2025-08-01 11:29:54,824 - app.main - INFO - FastAPI application started with all routes loaded
2025-08-01 11:31:11,369 - app.core.videodb - WARNING - VideoDB SDK not available, using mock implementation
2025-08-01 11:31:11,384 - app.core.videodb - INFO - Connected to VideoDB successfully
2025-08-01 11:31:12,423 - app.core.videodb - INFO - Found 2 existing collections
2025-08-01 11:31:13,170 - app.core.videodb - INFO - Found existing collection: convo ai (ID: c-2ac15f3e-4308-4895-9088-329dd7caf108)
2025-08-01 11:31:40,906 - app.main - INFO - FastAPI application started with all routes loaded
2025-08-01 11:32:45,047 - app.core.videodb - WARNING - VideoDB SDK not available, using mock implementation
2025-08-01 11:32:45,064 - app.core.videodb - INFO - Connected to VideoDB successfully
2025-08-01 11:32:46,644 - app.core.videodb - INFO - Found 2 existing collections
2025-08-01 11:32:47,456 - app.core.videodb - INFO - Found existing collection: convo ai (ID: c-2ac15f3e-4308-4895-9088-329dd7caf108)
2025-08-01 11:33:30,782 - app.main - INFO - FastAPI application started with all routes loaded
2025-08-01 11:33:37,402 - app.core.videodb - WARNING - VideoDB SDK not available, using mock implementation
2025-08-01 11:33:37,406 - app.core.videodb - INFO - Connected to VideoDB successfully
2025-08-01 11:33:38,272 - app.core.videodb - INFO - Found 2 existing collections
2025-08-01 11:33:39,047 - app.core.videodb - INFO - Found existing collection: convo ai (ID: c-2ac15f3e-4308-4895-9088-329dd7caf108)
