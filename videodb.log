2025-08-01 11:26:43,397 - app.core.videodb - WARNING - VideoDB SDK not available, using mock implementation
2025-08-01 11:26:43,399 - app.core.videodb - ERROR - Failed to initialize VideoDB connection: No API key provided. Set an API key either as an environment variable (VIDEO_DB_API_KEY) or pass it as an argument. 
2025-08-01 11:29:19,250 - app.core.videodb - WARNING - VideoDB SDK not available, using mock implementation
2025-08-01 11:29:19,253 - app.core.videodb - INFO - Connected to VideoDB successfully
2025-08-01 11:29:20,331 - app.core.videodb - INFO - Found 1 existing collections
2025-08-01 11:29:21,122 - app.core.videodb - INFO - Created new collection: convo ai (ID: c-2ac15f3e-4308-4895-9088-329dd7caf108)
2025-08-01 11:29:21,127 - app.core.videodb - INFO - Created new collection: convo ai (ID: c-2ac15f3e-4308-4895-9088-329dd7caf108)
2025-08-01 11:29:54,824 - app.main - INFO - FastAPI application started with all routes loaded
2025-08-01 11:31:11,369 - app.core.videodb - WARNING - VideoDB SDK not available, using mock implementation
2025-08-01 11:31:11,384 - app.core.videodb - INFO - Connected to VideoDB successfully
2025-08-01 11:31:12,423 - app.core.videodb - INFO - Found 2 existing collections
2025-08-01 11:31:13,170 - app.core.videodb - INFO - Found existing collection: convo ai (ID: c-2ac15f3e-4308-4895-9088-329dd7caf108)
2025-08-01 11:31:40,906 - app.main - INFO - FastAPI application started with all routes loaded
2025-08-01 11:32:45,047 - app.core.videodb - WARNING - VideoDB SDK not available, using mock implementation
2025-08-01 11:32:45,064 - app.core.videodb - INFO - Connected to VideoDB successfully
2025-08-01 11:32:46,644 - app.core.videodb - INFO - Found 2 existing collections
2025-08-01 11:32:47,456 - app.core.videodb - INFO - Found existing collection: convo ai (ID: c-2ac15f3e-4308-4895-9088-329dd7caf108)
2025-08-01 11:33:30,782 - app.main - INFO - FastAPI application started with all routes loaded
2025-08-01 11:33:37,402 - app.core.videodb - WARNING - VideoDB SDK not available, using mock implementation
2025-08-01 11:33:37,406 - app.core.videodb - INFO - Connected to VideoDB successfully
2025-08-01 11:33:38,272 - app.core.videodb - INFO - Found 2 existing collections
2025-08-01 11:33:39,047 - app.core.videodb - INFO - Found existing collection: convo ai (ID: c-2ac15f3e-4308-4895-9088-329dd7caf108)
2025-08-01 11:34:03,346 - app.main - INFO - FastAPI application started with all routes loaded
2025-08-01 11:34:44,392 - app.routes.search - INFO - Debug: Getting all video IDs from collection
2025-08-01 11:34:44,558 - app.core.videodb - INFO - Getting all video IDs from VideoDB collection
2025-08-01 11:34:47,727 - app.core.videodb - INFO - No videos found in collection
2025-08-01 11:34:47,745 - app.routes.search - INFO - Successfully retrieved video information: {'total_videos': 0, 'videos': [], 'error': None}
2025-08-01 11:36:14,710 - app.routes.upload - INFO - Received YouTube upload request for URL: https://youtu.be/7rBTbbfDUH0?si=Fc-kNk7Lu6DcuX1F
2025-08-01 11:36:14,746 - app.routes.upload - INFO - YouTube URL validation passed
2025-08-01 11:36:14,746 - app.routes.upload - INFO - Uploading YouTube video to VideoDB
2025-08-01 11:39:16,074 - app.core.videodb - INFO - Video uploaded to VideoDB: m-z-0198643f-020c-7a72-b96a-709f604192dd
2025-08-01 11:39:16,105 - app.routes.upload - INFO - Video uploaded to VideoDB: m-z-0198643f-020c-7a72-b96a-709f604192dd
2025-08-01 11:42:14,422 - app.core.videodb - INFO - Video m-z-0198643f-020c-7a72-b96a-709f604192dd indexed successfully
2025-08-01 11:42:14,560 - app.routes.search - INFO - Starting question processing for video m-z-0198643f-020c-7a72-b96a-709f604192dd
2025-08-01 11:42:14,560 - app.routes.search - INFO - Question: Give me the information wat in that video tells ?
2025-08-01 11:42:14,562 - app.routes.search - INFO - Use enhanced search: True
2025-08-01 11:42:14,562 - app.routes.search - INFO - Using enhanced video scene search...
2025-08-01 11:42:14,564 - app.core.videodb - INFO - Searching video scenes for: 'Give me the information wat in that video tells ?' in video m-z-0198643f-020c-7a72-b96a-709f604192dd
2025-08-01 11:42:18,028 - app.core.videodb - INFO - Search result attributes: ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__weakref__', '_connection', '_format_results', '_results', 'collection_id', 'compile', 'get_shots', 'play', 'player_url', 'shots', 'stream_url']
2025-08-01 11:42:20,698 - app.core.videodb - INFO - Generated compiled stream URL for 1 shots: https://stream.videodb.io/v3/published/manifests/a7296726-0321-4093-98fb-9e36b8b23e03.m3u8
2025-08-01 11:42:20,698 - app.core.videodb - INFO - Shot attributes: ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__getitem__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__weakref__', '_connection', 'end', 'generate_stream', 'play', 'player_url', 'search_score', 'start', 'stream_url', 'text', 'video_id', 'video_length', 'video_title']
2025-08-01 11:42:20,698 - app.core.videodb - INFO - Shot stream_url: None
2025-08-01 11:42:20,707 - app.core.videodb - INFO - Shot player_url: None
2025-08-01 11:42:20,709 - app.core.videodb - INFO - URL attribute player_url: None
2025-08-01 11:42:20,709 - app.core.videodb - INFO - URL attribute stream_url: None
2025-08-01 11:42:23,330 - app.core.videodb - INFO - Generated stream URL for shot 0: https://stream.videodb.io/v3/published/manifests/528820c3-5542-4501-8ba2-aca551aa42d9.m3u8
2025-08-01 11:42:23,332 - app.core.videodb - INFO - Returning compiled search result with 1 shots
2025-08-01 11:42:23,334 - app.core.videodb - INFO - Consolidated text length: 1169 characters
2025-08-01 11:42:23,416 - app.routes.search - INFO - Found 1 video segments
2025-08-01 11:42:25,173 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-01 11:42:29,159 - chromadb.telemetry.product.posthog - ERROR - Failed to send telemetry event ClientStartEvent: capture() takes 1 positional argument but 3 were given
2025-08-01 11:42:29,159 - app.core.chroma_rag - INFO - ChromaDB client initialized successfully
2025-08-01 11:42:29,239 - chromadb.telemetry.product.posthog - ERROR - Failed to send telemetry event ClientCreateCollectionEvent: capture() takes 1 positional argument but 3 were given
2025-08-01 11:43:06,298 - chromadb.telemetry.product.posthog - ERROR - Failed to send telemetry event CollectionQueryEvent: capture() takes 1 positional argument but 3 were given
2025-08-01 11:43:06,298 - app.routes.search - WARNING - No relevant text chunks found
2025-08-01 11:43:06,298 - app.routes.search - INFO - Generating answer with open-source LLM (Llama 2 or Mistral)...
2025-08-01 11:43:11,979 - app.routes.search - ERROR - Failed to get response from open-source LLM: Could not load model TheBloke/Mistral-7B-Instruct-v0.2-GGUF with any of the following classes: (<class 'transformers.models.auto.modeling_auto.AutoModelForCausalLM'>,). See the original errors:

while loading with AutoModelForCausalLM, an error is thrown:
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Telegram_bot\tts_env\lib\site-packages\transformers\pipelines\base.py", line 292, in infer_framework_load_model
    model = model_class.from_pretrained(model, **kwargs)
  File "C:\Users\<USER>\Downloads\Telegram_bot\tts_env\lib\site-packages\transformers\models\auto\auto_factory.py", line 600, in from_pretrained
    return model_class.from_pretrained(
  File "C:\Users\<USER>\Downloads\Telegram_bot\tts_env\lib\site-packages\transformers\modeling_utils.py", line 315, in _wrapper
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Downloads\Telegram_bot\tts_env\lib\site-packages\transformers\modeling_utils.py", line 4854, in from_pretrained
    checkpoint_files, sharded_metadata = _get_resolved_checkpoint_files(
  File "C:\Users\<USER>\Downloads\Telegram_bot\tts_env\lib\site-packages\transformers\modeling_utils.py", line 1247, in _get_resolved_checkpoint_files
    raise OSError(
OSError: TheBloke/Mistral-7B-Instruct-v0.2-GGUF does not appear to have a file named pytorch_model.bin, model.safetensors, tf_model.h5, model.ckpt or flax_model.msgpack.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Telegram_bot\tts_env\lib\site-packages\transformers\pipelines\base.py", line 310, in infer_framework_load_model
    model = model_class.from_pretrained(model, **fp32_kwargs)
  File "C:\Users\<USER>\Downloads\Telegram_bot\tts_env\lib\site-packages\transformers\models\auto\auto_factory.py", line 600, in from_pretrained
    return model_class.from_pretrained(
  File "C:\Users\<USER>\Downloads\Telegram_bot\tts_env\lib\site-packages\transformers\modeling_utils.py", line 315, in _wrapper
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Downloads\Telegram_bot\tts_env\lib\site-packages\transformers\modeling_utils.py", line 4854, in from_pretrained
    checkpoint_files, sharded_metadata = _get_resolved_checkpoint_files(
  File "C:\Users\<USER>\Downloads\Telegram_bot\tts_env\lib\site-packages\transformers\modeling_utils.py", line 1247, in _get_resolved_checkpoint_files
    raise OSError(
OSError: TheBloke/Mistral-7B-Instruct-v0.2-GGUF does not appear to have a file named pytorch_model.bin, model.safetensors, tf_model.h5, model.ckpt or flax_model.msgpack.



2025-08-01 11:56:49,191 - app.routes.upload - INFO - Received YouTube upload request for URL: https://youtu.be/9M05EjlhokM?si=CTt1CTtoOsHRVgkf
2025-08-01 11:56:49,221 - app.routes.upload - INFO - YouTube URL validation passed
2025-08-01 11:56:49,223 - app.routes.upload - INFO - Uploading YouTube video to VideoDB
2025-08-01 11:57:55,653 - app.core.videodb - INFO - Video uploaded to VideoDB: m-z-01986450-4163-7c73-b6ed-635c2429aec2
2025-08-01 11:57:55,671 - app.routes.upload - INFO - Video uploaded to VideoDB: m-z-01986450-4163-7c73-b6ed-635c2429aec2
2025-08-01 11:58:34,079 - app.core.videodb - INFO - Video m-z-01986450-4163-7c73-b6ed-635c2429aec2 indexed successfully
2025-08-01 11:58:34,084 - app.routes.search - INFO - Starting question processing for video m-z-01986450-4163-7c73-b6ed-635c2429aec2
2025-08-01 11:58:34,094 - app.routes.search - INFO - Question: what is the main idea of this video
2025-08-01 11:58:34,094 - app.routes.search - INFO - Use enhanced search: True
2025-08-01 11:58:34,095 - app.routes.search - INFO - Using enhanced video scene search...
2025-08-01 11:58:34,095 - app.core.videodb - INFO - Searching video scenes for: 'what is the main idea of this video' in video m-z-01986450-4163-7c73-b6ed-635c2429aec2
2025-08-01 11:58:46,848 - app.core.videodb - ERROR - Error searching video scenes: Invalid request: No results found. 
2025-08-01 11:58:46,850 - app.routes.search - INFO - VideoSearch response: {'collection_id': None, 'player_url': None, 'text': '', 'shots': [], 'error': 'Invalid request: No results found. '}
2025-08-01 11:58:46,850 - app.routes.search - WARNING - No video segments found in search response
2025-08-01 11:58:46,915 - chromadb.telemetry.product.posthog - ERROR - Failed to send telemetry event ClientCreateCollectionEvent: capture() takes 1 positional argument but 3 were given
2025-08-01 11:58:47,275 - app.routes.search - WARNING - No relevant text chunks found
2025-08-01 11:58:47,275 - app.routes.search - WARNING - No relevant content found for the question
2025-08-01 11:59:40,470 - app.routes.upload - INFO - Received YouTube upload request for URL: https://youtu.be/-5SOvWaW_OY?si=QPdtw63L80m9ktLi
2025-08-01 11:59:40,470 - app.routes.upload - INFO - YouTube URL validation passed
2025-08-01 11:59:40,470 - app.routes.upload - INFO - Uploading YouTube video to VideoDB
2025-08-01 12:00:16,236 - app.core.videodb - INFO - Video uploaded to VideoDB: m-z-01986452-cce0-7031-bad0-26fe2457cf34
2025-08-01 12:00:16,237 - app.routes.upload - INFO - Video uploaded to VideoDB: m-z-01986452-cce0-7031-bad0-26fe2457cf34
2025-08-01 12:00:55,032 - app.core.videodb - INFO - Video m-z-01986452-cce0-7031-bad0-26fe2457cf34 indexed successfully
2025-08-01 12:01:50,270 - app.core.videodb - WARNING - VideoDB SDK not available, using mock implementation
2025-08-01 12:01:50,272 - app.core.videodb - INFO - Connected to VideoDB successfully
2025-08-01 12:01:52,281 - app.core.videodb - INFO - Found 2 existing collections
2025-08-01 12:01:53,136 - app.core.videodb - INFO - Found existing collection: convo ai (ID: c-2ac15f3e-4308-4895-9088-329dd7caf108)
2025-08-01 12:02:23,474 - app.main - INFO - FastAPI application started with all routes loaded
2025-08-01 12:03:16,558 - app.core.videodb - WARNING - VideoDB SDK not available, using mock implementation
2025-08-01 12:03:16,558 - app.core.videodb - INFO - Connected to VideoDB successfully
2025-08-01 12:03:17,540 - app.core.videodb - INFO - Found 2 existing collections
2025-08-01 12:03:18,302 - app.core.videodb - INFO - Found existing collection: convo ai (ID: c-2ac15f3e-4308-4895-9088-329dd7caf108)
